# Rubick Auto Ultimate Steal для Hake.me

Автоматический скрипт для кражи ультимативных способностей в Dota 2 для героя Rubick, специально разработанный для платформы Hake.me.

## Ключевые особенности

- **Глобальное отслеживание ультов** - отслеживает использование ультимативных способностей всех врагов на карте
- **Умная система памяти** - запоминает последний использованный ульт каждого героя
- **Динамический радиус кражи** - автоматически учитывает предметы, увеличивающие дальность (Aether Lens, Dragon Lance)
- **Черный список** - избегает кражи бесполезных или опасных ультимативных способностей
- **Интеграция с Hake.me** - использует встроенную систему меню и настроек

## Установка

1. Поместите файл `rubick_auto_steal.lua` в папку со скриптами Hake.me
2. Загрузите скрипт через интерфейс Hake.me
3. Выберите героя Rubick в игре

## Настройки в меню Hake.me

Все настройки доступны в меню: **Hero Specific → Rubick → Auto Steal**

- **Enable Auto Steal** - включить/выключить автоматическую кражу
- **Steal Cooldown** - задержка между кражами (1-5 секунд)
- **Show Notifications** - показывать уведомления о краже
- **Show Blacklist GUI** - показать окно управления черным списком
- **Replace Spell on Cooldown** - заменять способность, если текущая на кулдауне больше 5 секунд

## Логика кражи ультимативных способностей

Скрипт крадет **все ультимативные способности**, кроме тех, что находятся в черном списке.

### Черный список (не крадет):

**Самоубийственные:**
- Suicide / Focused Detonate (Techies)

**Пассивные или бесполезные для Rubick:**
- Borrowed Time (Abaddon) - пассивная активация
- Reincarnation (Wraith King) - работает только при смерти
- Divided We Stand (Meepo) - не работает у Rubick
- Marksmanship (Drow Ranger) - пассивная

**Трансформации:**
- True Form (Lone Druid)
- Elder Dragon Form (Dragon Knight)
- Metamorphosis (Terrorblade)
- Chemical Rage (Alchemist)
- Shapeshift (Lycan)

**Требующие особого использования:**
- Rupture (Bloodseeker) - требует правильного применения
- Assassinate (Sniper) - долгое время каста
- Dismember (Pudge) - может быть опасно без правильного позиционирования

## GUI для управления черным списком

### Как использовать:
1. Включите настройку **"Show Blacklist GUI"** в меню Hake.me
2. Откроется окно **"Rubick Ultimate Blacklist"**
3. В окне отображаются все ультимативные способности врагов в текущей игре
4. Отметьте галочками способности, которые НЕ хотите красть

### Возможности GUI:
- **Список врагов** - показывает только ультимативные способности врагов в текущей игре
- **Красивые имена** - отображает читаемые имена героев (например, "Anti Mage" вместо "npc_dota_hero_antimage")
- **Кнопка "Clear All"** - убирает все способности из черного списка
- **Кнопка "Blacklist All"** - добавляет все ультимативные способности врагов в черный список
- **Глобальный черный список** - показывает способности, которые всегда заблокированы

### Преимущества:
- **Адаптивность** - список обновляется для каждой игры
- **Удобство** - не нужно помнить названия способностей
- **Гибкость** - можно быстро изменить настройки прямо в игре

## Как работает скрипт

### Алгоритм работы

1. **Глобальное отслеживание** - скрипт постоянно отслеживает всех вражеских героев на карте
2. **Фиксация ультов** - когда враг использует ультимативную способность, скрипт запоминает это
3. **Проверка дистанции** - когда враг с запомненным ультом входит в радиус Spell Steal, скрипт готов к краже
4. **Динамический радиус** - автоматически получает текущую дальность кастов с учетом всех модификаторов
5. **Проверка черного списка** - избегает кражи бесполезных ультимативных способностей
6. **Кража** - использует Spell Steal на подходящую цель

### Динамическое определение радиуса

Скрипт использует `NPC.GetCastRangeBonus()` для автоматического учета всех модификаторов дальности:
- **Aether Lens** (+220 к дальности)
- **Нейтральные предметы** (Grove Bow, Telescope, etc.)
- **Таланты героя** (если влияют на дальность кастов)
- **Баффы и дебаффы** (любые эффекты, изменяющие дальность)
- **Другие предметы** (любые будущие предметы, влияющие на дальность)

### Продвинутое отслеживание

- **Отслеживание через `Ability.SecondsSinceLastUse()`** - более точное определение времени использования
- **Проверка всех слотов способностей** (0-24) для учета Aghanim's Shard и других модификаций
- **Отслеживание toggle-способностей** - учитывает способности в активном состоянии
- **Звуковое отслеживание** - дополнительно отслеживает ульты через звуки для невидимых кастов

### Система памяти

- Запоминает последний использованный ульт каждого вражеского героя
- Автоматически очищает устаревшие записи (старше 30 секунд)
- Помечает украденные способности, чтобы не красть их повторно
- Сохраняет данные даже после смерти героя - при возрождении ульт все равно будет украден

## Преимущества перед обычными скриптами

### Глобальное отслеживание
- **Обычные скрипты** крадут только то, что видят в данный момент
- **Этот скрипт** запоминает ульты, использованные в любой точке карты

### Умная кража
- Не пытается красть способности, которые уже украл
- Ждет оптимального момента для кражи
- Учитывает приоритет способностей

### Адаптивность
- Автоматически подстраивается под изменения дальности кастов
- Работает с любыми предметами и баффами

## Требования

- **Hake.me** - платформа для скриптов
- **Dota 2** - игра должна быть запущена
- **Герой Rubick** - скрипт работает только с этим героем

## Устранение неполадок

**Скрипт не работает:**
1. Убедитесь, что играете за Rubick
2. Проверьте, что Spell Steal изучен (хотя бы 1 уровень)
3. Убедитесь, что скрипт включен в настройках Hake.me

**Не крадет способности:**
1. Убедитесь, что враги действительно используют ультимативные способности
2. Проверьте, что у вас достаточно маны для Spell Steal
3. Возможно, все доступные ульты находятся в черном списке

**Крадет не те способности:**
1. Проверьте черный список - возможно, нужно добавить/убрать некоторые способности
2. Настройте задержку между кражами, если скрипт слишком активен

## Дисклеймер

Этот скрипт создан для образовательных целей и демонстрации возможностей платформы Hake.me. Использование любых сторонних программ в онлайн-играх может нарушать пользовательское соглашение и привести к блокировке аккаунта. Используйте на свой страх и риск.
