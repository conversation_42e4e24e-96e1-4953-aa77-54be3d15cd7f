-- Rubick Auto Ultimate Steal Script for Hake.me
-- Автоматически ворует ультимативные способности врагов

local RubickAutoSteal = {}

-- Настройки скрипта
local SETTINGS = {
    enabled = Menu.AddOptionBool({"Hero Specific", "Rubick", "Auto Steal"}, "Enable Auto Steal", true),
    steal_cooldown = Menu.AddOptionSlider({"Hero Specific", "Rubick", "Auto Steal"}, "Steal Cooldown", 1, 5, 2),
    show_notifications = Menu.AddOptionBool({"Hero Specific", "Rubick", "Auto Steal"}, "Show Notifications", true),
    show_blacklist_gui = Menu.AddOptionBool({"Hero Specific", "Rubick", "Auto Steal"}, "Show Blacklist GUI", false)
}

-- Глобальные переменные
local myHero = nil
local spellSteal = nil
local lastStealTime = 0
local enemyLastUltimates = {} -- Последние ультимативные способности каждого врага
local ultimateAbilities = {} -- Кэш ультимативных способностей
local currentGameBlacklist = {} -- Черный список для текущей игры
local enemyUltimatesInGame = {} -- Ультимативные способности врагов в текущей игре
local soundTrackingData = {} -- Данные для отслеживания звуков

-- Бесполезные или опасные ультимативные способности (черный список)
local BLACKLISTED_ULTIMATES = {
    "techies_suicide", "techies_focused_detonate",
    "abaddon_borrowed_time",
    "wraith_king_reincarnation",
    "skeleton_king_reincarnation",
    "meepo_divided_we_stand",
    "lone_druid_true_form",
    "dragon_knight_elder_dragon_form",
    "terrorblade_metamorphosis",
    "alchemist_chemical_rage",
    "lycan_shapeshift",
    "bloodseeker_rupture", -- Требует правильного использования
    "drow_ranger_marksmanship", -- Пассивная
    "sniper_assassinate", -- Долгое время каста, легко прерывается
    "pudge_dismember" -- Может быть опасно без правильного позиционирования
}

-- Инициализация
function RubickAutoSteal.Init()
    myHero = Heroes.GetLocal()
    if not myHero or NPC.GetUnitName(myHero) ~= "npc_dota_hero_rubick" then
        return false
    end

    spellSteal = NPC.GetAbility(myHero, "rubick_spell_steal")
    if not spellSteal then
        return false
    end

    -- Инициализируем кэш ультимативных способностей
    RubickAutoSteal.InitUltimateCache()

    return true
end

-- Инициализация кэша ультимативных способностей
function RubickAutoSteal.InitUltimateCache()
    enemyUltimatesInGame = {} -- Очищаем список ультов в игре

    for i = 1, Heroes.Count() do
        local hero = Heroes.Get(i)
        if hero and not Entity.IsSameTeam(myHero, hero) then
            local heroIndex = Entity.GetIndex(hero)
            local ultimate = RubickAutoSteal.GetHeroUltimate(hero)
            ultimateAbilities[heroIndex] = ultimate

            -- Добавляем ультимат в список для GUI
            if ultimate then
                local abilityName = Ability.GetName(ultimate)
                local heroName = NPC.GetUnitName(hero)

                -- Убираем префикс "npc_dota_hero_" для красивого отображения
                local displayHeroName = string.gsub(heroName, "npc_dota_hero_", "")
                displayHeroName = string.gsub(displayHeroName, "_", " ")
                displayHeroName = string.upper(string.sub(displayHeroName, 1, 1)) .. string.sub(displayHeroName, 2)

                enemyUltimatesInGame[abilityName] = {
                    heroName = displayHeroName,
                    abilityName = abilityName,
                    ability = ultimate
                }
            end
        end
    end
end

-- Получение ультимативной способности героя
function RubickAutoSteal.GetHeroUltimate(hero)
    if not hero then return nil end

    for i = 0, 5 do
        local ability = NPC.GetAbilityByIndex(hero, i)
        if ability and Ability.IsUltimate(ability) then
            return ability
        end
    end
    return nil
end

-- Проверка, является ли способность ультимативной
function RubickAutoSteal.IsUltimateAbility(ability)
    if not ability then return false end
    return Ability.IsUltimate(ability)
end

-- Проверка, находится ли ультимат в черном списке
function RubickAutoSteal.IsBlacklistedUltimate(abilityName)
    -- Проверяем глобальный черный список
    for _, blacklisted in ipairs(BLACKLISTED_ULTIMATES) do
        if string.find(abilityName, blacklisted) then
            return true
        end
    end

    -- Проверяем черный список текущей игры
    return currentGameBlacklist[abilityName] == true
end

-- Получение текущей дальности кражи с учетом всех модификаторов
function RubickAutoSteal.GetStealRange()
    if not spellSteal then return 0 end

    -- Используем API для получения текущей дальности способности с учетом всех модификаторов
    -- Это автоматически учтет Aether Lens, нейтральные предметы, таланты, баффы и т.д.
    local currentRange = NPC.GetCastRangeBonus(myHero) + Ability.GetCastRange(spellSteal)

    return currentRange
end

-- Отслеживание использования ультимативных способностей
function RubickAutoSteal.TrackUltimateUsage()
    for i = 1, Heroes.Count() do
        local enemy = Heroes.Get(i)
        if enemy and Entity.IsAlive(enemy) and not Entity.IsSameTeam(myHero, enemy) then
            local heroIndex = Entity.GetIndex(enemy)

            -- Проверяем все способности героя, не только кэшированные ульты
            for abilityIndex = 0, 24 do
                local ability = NPC.GetAbilityByIndex(enemy, abilityIndex)
                if ability and not Ability.IsHidden(ability) and not Ability.IsAttributes(ability)
                   and not Ability.IsPassive(ability) and Ability.IsUltimate(ability) then

                    local secondsSinceUse = Ability.SecondsSinceLastUse(ability)
                    local abilityName = Ability.GetName(ability)

                    -- Если способность была использована недавно (в последние 0.5 секунды)
                    -- или если способность в состоянии toggle
                    if (secondsSinceUse > 0 and secondsSinceUse <= 0.5) or Ability.GetToggleState(ability) then
                        if not RubickAutoSteal.IsBlacklistedUltimate(abilityName) then
                            -- Сохраняем информацию о последнем использованном ульте
                            enemyLastUltimates[heroIndex] = {
                                ability = ability,
                                name = abilityName,
                                time = GameRules.GetGameTime(),
                                stolen = false
                            }
                        end
                    end
                end
            end
        end
    end
end

-- Проверка приоритета кражи (как в вашем примере)
function RubickAutoSteal.ShouldStealUltimate(lastUlt, enemy)
    local hasAghs = NPC.GetItem(myHero, "item_ultimate_scepter") or
                   NPC.HasModifier(myHero, "modifier_item_ultimate_scepter_consumed")

    if hasAghs then
        -- С Aghanim's Scepter крадем все ультимативные способности
        return true
    else
        -- Без Aghanim's Scepter применяем более строгие критерии
        local ability = lastUlt.ability
        if not ability then return false end

        -- Крадем если это:
        -- 1. Ультимативная способность
        -- 2. Способность 4 уровня
        -- 3. Способность с Dispellable Type = 1 (сильная)
        -- 4. Особые способности Invoker'а
        return Ability.IsUltimate(ability) or
               Ability.GetLevel(ability) == 4 or
               Ability.GetDispellableType(ability) == 1 or
               string.find(lastUlt.name, "invoker_cold_snap") or
               string.find(lastUlt.name, "invoker_emp") or
               string.find(lastUlt.name, "invoker_chaos_meteor") or
               string.find(lastUlt.name, "invoker_deafening_blast")
    end
end

-- Проверка, есть ли у Rubick уже эта способность
function RubickAutoSteal.AlreadyHasSpell(abilityName)
    -- Проверяем украденную способность (слот 3 или 4)
    local stolenSpell1 = NPC.GetAbilityByIndex(myHero, 3)
    local stolenSpell2 = NPC.GetAbilityByIndex(myHero, 4)

    if stolenSpell1 and Ability.GetName(stolenSpell1) == abilityName then
        return true
    end

    if stolenSpell2 and Ability.GetName(stolenSpell2) == abilityName then
        return true
    end

    -- Проверяем, есть ли пустой слот (rubick_empty1)
    local emptySlot = NPC.GetAbility(myHero, "rubick_empty1")
    if emptySlot then
        return false -- Есть пустой слот, можно красть
    end

    return false
end

-- Основная логика кражи ультимативных способностей
function RubickAutoSteal.TryStealUltimate()
    if not Menu.GetValue(SETTINGS.enabled) then return end
    if not myHero or not Entity.IsAlive(myHero) then return end
    if not spellSteal or not Ability.IsReady(spellSteal) then return end

    local currentTime = GameRules.GetGameTime()
    local cooldown = Menu.GetValue(SETTINGS.steal_cooldown)
    if currentTime - lastStealTime < cooldown then return end

    local stealRange = RubickAutoSteal.GetStealRange()
    local myPos = Entity.GetAbsOrigin(myHero)
    local bestTarget = nil
    local bestUltimate = nil

    -- Проверяем всех врагов в радиусе кражи
    for i = 1, Heroes.Count() do
        local enemy = Heroes.Get(i)
        if enemy and Entity.IsAlive(enemy) and not Entity.IsSameTeam(myHero, enemy) then
            local heroIndex = Entity.GetIndex(enemy)
            local lastUlt = enemyLastUltimates[heroIndex]

            if lastUlt and not lastUlt.stolen then
                -- Проверяем, что ульт не в черном списке
                if not RubickAutoSteal.IsBlacklistedUltimate(lastUlt.name) then
                    -- Проверяем, что у нас еще нет этой способности
                    if not RubickAutoSteal.AlreadyHasSpell(lastUlt.name) then
                        -- Проверяем приоритет кражи
                        if RubickAutoSteal.ShouldStealUltimate(lastUlt, enemy) then
                            local enemyPos = Entity.GetAbsOrigin(enemy)
                            local distance = (myPos - enemyPos):Length()

                            if distance <= stealRange then
                                -- Дополнительные проверки безопасности
                                if not NPC.HasState(enemy, Enum.ModifierState.MODIFIER_STATE_INVULNERABLE) and
                                   not NPC.HasModifier(enemy, "modifier_dark_willow_shadow_realm_buff") then
                                    bestTarget = enemy
                                    bestUltimate = lastUlt
                                    break
                                end
                            end
                        end
                    else
                        -- Помечаем как украденную, если у нас уже есть эта способность
                        lastUlt.stolen = true
                        if Menu.GetValue(SETTINGS.show_notifications) then
                            print("Уже есть способность: " .. lastUlt.name)
                        end
                    end
                end
            end
        end
    end

    -- Крадем найденный ульт
    if bestTarget and bestUltimate then
        Ability.CastTarget(spellSteal, bestTarget)
        lastStealTime = currentTime
        bestUltimate.stolen = true

        if Menu.GetValue(SETTINGS.show_notifications) then
            local source = bestUltimate.source or "tracking"
            local message = "Украл: " .. bestUltimate.name .. " у " .. NPC.GetUnitName(bestTarget) .. " (" .. source .. ")"
            print(message)
        end
    end
end

-- Обновление каждый кадр
function RubickAutoSteal.OnUpdate()
    if not myHero then return end

    -- Отслеживаем использование ультимативных способностей
    RubickAutoSteal.TrackUltimateUsage()

    -- Пытаемся украсть ультимативную способность
    RubickAutoSteal.TryStealUltimate()

    -- Очищаем старые записи (старше 30 секунд)
    local currentTime = GameRules.GetGameTime()
    for heroIndex, ultData in pairs(enemyLastUltimates) do
        if currentTime - ultData.time > 30 then
            enemyLastUltimates[heroIndex] = nil
        end
    end
end

-- Инициализация при загрузке игры
function RubickAutoSteal.OnGameStart()
    RubickAutoSteal.Init()
end



-- Обработка звуков для отслеживания невидимых кастов (улучшенная версия)
function RubickAutoSteal.OnStartSound(sound)
    if not sound or not sound.name then return end

    local soundName = sound.name

    -- Сохраняем данные о звуке для последующего сопоставления
    soundTrackingData.lastSoundName = soundName
    soundTrackingData.lastSoundTime = GameRules.GetGameTime()

    -- Отслеживаем звуки ультимативных способностей
    for i = 1, Heroes.Count() do
        local enemy = Heroes.Get(i)
        if enemy and not Entity.IsSameTeam(myHero, enemy) then
            local heroIndex = Entity.GetIndex(enemy)
            local heroName = NPC.GetUnitName(enemy)

            -- Проверяем все ультимативные способности героя
            for abilityIndex = 0, 24 do
                local ability = NPC.GetAbilityByIndex(enemy, abilityIndex)
                if ability and Ability.IsUltimate(ability) then
                    local abilityName = Ability.GetName(ability)

                    -- Более точная проверка соответствия звука и способности
                    if string.find(soundName, abilityName) or
                       string.find(soundName, string.gsub(abilityName, ".*_", "")) or
                       string.find(soundName, heroName) then

                        if not RubickAutoSteal.IsBlacklistedUltimate(abilityName) then
                            enemyLastUltimates[heroIndex] = {
                                ability = ability,
                                name = abilityName,
                                time = GameRules.GetGameTime(),
                                stolen = false,
                                source = "sound"
                            }

                            -- Сохраняем связь звука с героем (как в вашем примере)
                            soundTrackingData.lastHeroName = heroName
                            soundTrackingData.lastAbilityName = abilityName
                        end
                    end
                end
            end
        end
    end
end

-- GUI для управления черным списком
function RubickAutoSteal.OnUI()
    if not Menu.GetValue(SETTINGS.show_blacklist_gui) then return end

    if UI.BeginWindow("Rubick Ultimate Blacklist") then
        UI.Text("Blacklist ultimates you don't want to steal:")
        UI.Separator()

        -- Показываем ультимативные способности врагов в текущей игре
        for abilityName, ultData in pairs(enemyUltimatesInGame) do
            local isBlacklisted = currentGameBlacklist[abilityName] == true
            local changed, newValue = UI.Checkbox(ultData.heroName .. " - " .. abilityName, isBlacklisted)

            if changed then
                if newValue then
                    currentGameBlacklist[abilityName] = true
                    if Menu.GetValue(SETTINGS.show_notifications) then
                        print("Added to blacklist: " .. abilityName)
                    end
                else
                    currentGameBlacklist[abilityName] = nil
                    if Menu.GetValue(SETTINGS.show_notifications) then
                        print("Removed from blacklist: " .. abilityName)
                    end
                end
            end
        end

        UI.Separator()

        -- Кнопки управления
        if UI.Button("Clear All") then
            currentGameBlacklist = {}
            if Menu.GetValue(SETTINGS.show_notifications) then
                print("Cleared all blacklisted ultimates")
            end
        end

        UI.SameLine()

        if UI.Button("Blacklist All") then
            for abilityName, _ in pairs(enemyUltimatesInGame) do
                currentGameBlacklist[abilityName] = true
            end
            if Menu.GetValue(SETTINGS.show_notifications) then
                print("Blacklisted all enemy ultimates")
            end
        end

        UI.Separator()
        UI.Text("Global blacklist (always blocked):")
        UI.TextWrapped(table.concat(BLACKLISTED_ULTIMATES, ", "))

        UI.EndWindow()
    end
end

-- Регистрация коллбэков
function RubickAutoSteal.Start()
    -- Регистрируем коллбэки
    Callbacks.Register("Update", RubickAutoSteal.OnUpdate)
    Callbacks.Register("GameStart", RubickAutoSteal.OnGameStart)
    Callbacks.Register("StartSound", RubickAutoSteal.OnStartSound)
    Callbacks.Register("UI", RubickAutoSteal.OnUI)

    -- Инициализируем, если игра уже началась
    if GameRules.GetGameState() >= DOTA_GAMERULES_STATE_GAME_IN_PROGRESS then
        RubickAutoSteal.Init()
    end
end

-- Запуск скрипта
RubickAutoSteal.Start()

return RubickAutoSteal
